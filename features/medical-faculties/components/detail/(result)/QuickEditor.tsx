import StarIcon from '@/assets/icons/star-fly.svg'
import { Text } from "@/components/ui/Text/Text"
import { useState } from 'react'
import { useTranslation } from "react-i18next"
import { Modal, TouchableOpacity, View } from "react-native"
import { useSafeAreaInsets } from 'react-native-safe-area-context'

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import CheckIcon from '@/assets/icons/check-icon.svg'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { KeywordSelectionLayout } from '../../search/BottomSheetSelectKeyword'

export const QuickEditor = () => {
    const { t } = useTranslation()
    const insets = useSafeAreaInsets()

    const [isVisible, setIsVisible] = useState(false)

    const { openCustomSheet, closeSheet } = useSheetActions()

    const openSelectSymptom = () => {
        openCustomSheet({
            children: <KeywordSelectionLayout onApply={() => { }} />,
            baseProps: {
                snapPoints: ['70%', '70%'],
                enableDynamicSizing: false,
                enableOverDrag: false,
                style: {
                    zIndex: 9999,
                    elevation: 9999,
                }
            },

        })
    }

    return <View>
        <TouchableOpacity onPress={(() => setIsVisible(true))} className="flex-row gap-1 items-center py-1 px-2 rounded-md bg-[##DBEAFE]">
            <StarIcon />
            <Text size="body8" variant="primary">
                {t('MES-1035')}
            </Text>
        </TouchableOpacity>

        <Modal
            visible={isVisible}
            presentationStyle="fullScreen"
            animationType="slide"

        >
            <View style={{
                flex: 1,
                paddingTop: insets.top,
                paddingBottom: insets.bottom,
                paddingLeft: insets.left,
                paddingRight: insets.right,
            }}>
                <View style={{ flex: 1, paddingHorizontal: 16 }}>
                    <View className='flex-row gap-2 justify-between items-center'>
                        <TouchableOpacity className='p-2' onPress={() => setIsVisible(false)}>
                            <ArrowLeftIcon className='size-6' />
                        </TouchableOpacity>

                        <Text size="body3" variant="primary">
                            {t('MES-1036')}
                        </Text>

                        <TouchableOpacity className='p-2' onPress={() => setIsVisible(false)}>
                            <CheckIcon className='size-6' />
                        </TouchableOpacity>
                    </View>

                    <View className='mt-4 gap-2'>
                        <Text size='body6' className='w-full' >
                            {t('MES-1037')}
                        </Text>

                        <TouchableOpacity onPress={() => openSelectSymptom()} className='w-full px-4 py-3 rounded-lg border border-[#DDDDEE]'>
                            <Text size='field2' className='w-full' >
                                đau đầu, chóng mặt
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    </View>

}