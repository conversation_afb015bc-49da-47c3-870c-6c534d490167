import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { QuickEditFacultiesWrapper } from '@/features/medical-faculties/containers/quick-edit/QuickEditFacultiesWrapper'
import { Stack } from 'expo-router'
import { SafeAreaView } from 'react-native-safe-area-context'

export default function MedicalFacultiesQuickEdit() {
    return (
        <Stack>
            <StackScreenBase
                name='medical-faculties-quick-edit'
                options={{
                    headerShown: true,
                    presentation: 'modal'
                }}
            />
            <SafeAreaView
                className="bg-white"
                style={{ flex: 1 }}
                edges={['left', 'top', 'right', 'bottom']}
            >
                <QuickEditFacultiesWrapper />
            </SafeAreaView>
        </Stack>
    )
}
